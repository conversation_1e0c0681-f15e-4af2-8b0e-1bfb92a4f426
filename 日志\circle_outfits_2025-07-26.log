[2025-07-26 15:27:46] [API] API Request: GET /get_circle_outfits.php
Context: {
    "endpoint": "\/get_circle_outfits.php",
    "method": "GET",
    "params": {
        "user_id": "3",
        "page": 1,
        "per_page": 20,
        "category": "undefined"
    },
    "user_agent": "Mozilla\/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) Version\/15.0 Mobile\/15E148 Safari\/604.1 wechatdevtools\/1.06.2412050 MicroMessenger\/8.0.5 Language\/zh_CN webview\/",
    "ip": "*************"
}
--------------------------------------------------------------------------------
[2025-07-26 15:27:46] [DEBUG] 穿搭API - 圈子和用户基本信息
Context: {
    "user_id": "3",
    "circle_id": "1",
    "user_role": "member"
}
--------------------------------------------------------------------------------
[2025-07-26 15:27:46] [DEBUG] 用户穿搭分布
Context: {
    "user_id": "3",
    "circle_id": "1",
    "distribution": {
        "total": "4",
        "personal": "4",
        "shared": "0",
        "in_current_circle": "0"
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:27:46] [SQL] Database Query Executed
Context: {
    "sql": "主穿搭查询",
    "params": "SELECT o.id, o.name, o.category, o.category_id, o.thumbnail_url,\n                          o.description, o.created_at, o.updated_at, o.user_id, o.circle_id,\n                          o.outfit_data, o.is_public, o.likes_count,\n                          u.nickname as creator_nickname\n                   FROM outfits o\n                   JOIN users u ON o.user_id = u.id\n                   WHERE (o.circle_id = :circle_id OR \n                          (o.user_id IN (SELECT user_id FROM circle_members \n                                        WHERE circle_id = :circle_id AND status = 'active') \n                           AND o.circle_id IS NULL)) AND o.category = :category\n                   ORDER BY o.created_at DESC\n                   LIMIT :offset, :per_page",
    "result_count": 4,
    "result_sample": {
        "params": {
            "circle_id": "1",
            "category": "undefined"
        },
        "offset": 0,
        "per_page": 20
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:27:46] [DEBUG] 穿搭主查询执行结果
Context: {
    "row_count": 0,
    "expected_more_than_zero": false
}
--------------------------------------------------------------------------------
[2025-07-26 15:27:46] [API] API Request: GET /get_circle_outfits.php
Context: {
    "endpoint": "\/get_circle_outfits.php",
    "method": "GET",
    "params": {
        "user_id": "3",
        "circle_id": "1"
    },
    "user_agent": "Mozilla\/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) Version\/15.0 Mobile\/15E148 Safari\/604.1 wechatdevtools\/1.06.2412050 MicroMessenger\/8.0.5 Language\/zh_CN webview\/",
    "ip": "*************",
    "response": {
        "outfits_count": 0,
        "category_stats_count": 1,
        "total_count": "0"
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:06] [API] API Request: GET /get_circle_outfits.php
Context: {
    "endpoint": "\/get_circle_outfits.php",
    "method": "GET",
    "params": {
        "user_id": "3",
        "page": 1,
        "per_page": 20,
        "category": null
    },
    "user_agent": "Mozilla\/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) Version\/15.0 Mobile\/15E148 Safari\/604.1 wechatdevtools\/1.06.2412050 MicroMessenger\/8.0.5 Language\/zh_CN webview\/",
    "ip": "*************"
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:06] [DEBUG] 穿搭API - 圈子和用户基本信息
Context: {
    "user_id": "3",
    "circle_id": "1",
    "user_role": "member"
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:06] [DEBUG] 用户穿搭分布
Context: {
    "user_id": "3",
    "circle_id": "1",
    "distribution": {
        "total": "4",
        "personal": "0",
        "shared": "4",
        "in_current_circle": "4"
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:06] [SQL] Database Query Executed
Context: {
    "sql": "主穿搭查询",
    "params": "SELECT o.id, o.name, o.category, o.category_id, o.thumbnail_url,\n                          o.description, o.created_at, o.updated_at, o.user_id, o.circle_id,\n                          o.outfit_data, o.is_public, o.likes_count,\n                          u.nickname as creator_nickname\n                   FROM outfits o\n                   JOIN users u ON o.user_id = u.id\n                   WHERE (o.circle_id = :circle_id OR \n                          (o.user_id IN (SELECT user_id FROM circle_members \n                                        WHERE circle_id = :circle_id AND status = 'active') \n                           AND o.circle_id IS NULL))\n                   ORDER BY o.created_at DESC\n                   LIMIT :offset, :per_page",
    "result_count": 4,
    "result_sample": {
        "params": {
            "circle_id": "1"
        },
        "offset": 0,
        "per_page": 20
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:06] [DEBUG] 穿搭主查询执行结果
Context: {
    "row_count": 5,
    "expected_more_than_zero": true
}
--------------------------------------------------------------------------------
[2025-07-26 15:31:41] [API] API Request: GET /get_circle_outfits.php
Context: {
    "endpoint": "\/get_circle_outfits.php",
    "method": "GET",
    "params": {
        "user_id": "3",
        "page": 1,
        "per_page": 20,
        "category": null
    },
    "user_agent": "Mozilla\/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) Version\/15.0 Mobile\/15E148 Safari\/604.1 wechatdevtools\/1.06.2412050 MicroMessenger\/8.0.5 Language\/zh_CN webview\/",
    "ip": "*************"
}
--------------------------------------------------------------------------------
[2025-07-26 15:31:41] [DEBUG] 穿搭API - 圈子和用户基本信息
Context: {
    "user_id": "3",
    "circle_id": "1",
    "user_role": "member"
}
--------------------------------------------------------------------------------
[2025-07-26 15:31:41] [DEBUG] 用户穿搭分布
Context: {
    "user_id": "3",
    "circle_id": "1",
    "distribution": {
        "total": "4",
        "personal": "0",
        "shared": "4",
        "in_current_circle": "4"
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:31:41] [SQL] Database Query Executed
Context: {
    "sql": "主穿搭查询",
    "params": "SELECT o.id, o.name, o.category, o.category_id, o.thumbnail_url,\n                          o.description, o.created_at, o.updated_at, o.user_id, o.circle_id,\n                          o.outfit_data, o.is_public, o.likes_count,\n                          u.nickname as creator_nickname\n                   FROM outfits o\n                   JOIN users u ON o.user_id = u.id\n                   WHERE (o.circle_id = :circle_id OR \n                          (o.user_id IN (SELECT user_id FROM circle_members \n                                        WHERE circle_id = :circle_id AND status = 'active') \n                           AND o.circle_id IS NULL))\n                   ORDER BY o.created_at DESC\n                   LIMIT :offset, :per_page",
    "result_count": 4,
    "result_sample": {
        "params": {
            "circle_id": "1"
        },
        "offset": 0,
        "per_page": 20
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:31:41] [DEBUG] 穿搭主查询执行结果
Context: {
    "row_count": 5,
    "expected_more_than_zero": true
}
--------------------------------------------------------------------------------

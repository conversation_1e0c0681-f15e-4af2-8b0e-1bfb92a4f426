# 被踢出用户通知功能 - 测试验证清单

## 功能概述
当用户被踢出圈子后，再次进入穿搭圈子页面时会显示通知弹框，告知用户被移出的情况。

## 修复内容

### 1. 后端API修改
- **修改了 `get_circle_info.php`**：
  - 增加检测用户被踢出状态的逻辑
  - 返回被踢出的详细信息（圈子名称、操作者、时间等）
  - 支持已读状态检查，避免重复显示通知

- **新增了 `clear_removed_status.php`**：
  - 用于标记用户已读被踢出通知
  - 防止重复显示相同通知

### 2. 前端功能增强
- **增加了被踢出通知显示逻辑**：
  - 检测API返回的被踢出状态
  - 显示友好的通知弹框
  - 区分被踢出和主动退出的不同提示

- **优化了时间显示格式**：
  - 今天/昨天的相对时间显示
  - 超过7天显示具体日期

## 测试场景

### 场景1：用户被踢出后首次进入页面
**测试步骤**：
1. 用户A创建圈子，用户B加入圈子
2. 用户A踢出用户B
3. 用户B进入穿搭圈子页面

**预期结果**：
- [ ] 显示"您已被移出圈子"弹框
- [ ] 弹框内容包含圈子名称和操作者信息
- [ ] 弹框颜色为红色（#ff3b30）
- [ ] 点击"我知道了"后弹框消失

### 场景2：用户主动退出后进入页面
**测试步骤**：
1. 用户B重新加入圈子
2. 用户B主动退出圈子
3. 用户B进入穿搭圈子页面

**预期结果**：
- [ ] 显示"您已退出圈子"弹框
- [ ] 弹框内容显示主动退出信息
- [ ] 弹框颜色为蓝色（#007aff）
- [ ] 点击"我知道了"后弹框消失

### 场景3：用户已读通知后再次进入
**测试步骤**：
1. 用户B被踢出并已看过通知
2. 用户B再次进入穿搭圈子页面

**预期结果**：
- [ ] 不显示被踢出通知弹框
- [ ] 直接显示未加入圈子的引导页面

### 场景4：时间显示格式测试
**测试步骤**：
1. 测试今天被踢出的用户
2. 测试昨天被踢出的用户
3. 测试一周前被踢出的用户

**预期结果**：
- [ ] 今天：显示"今天 HH:MM"
- [ ] 昨天：显示"昨天 HH:MM"
- [ ] 一周内：显示"X天前"
- [ ] 超过一周：显示具体日期

### 场景5：网络异常处理
**测试步骤**：
1. 断网状态下进入页面
2. API返回错误时的处理

**预期结果**：
- [ ] 显示网络错误提示
- [ ] 不会显示被踢出通知
- [ ] 页面状态正常

## API测试

### 测试get_circle_info.php
**测试用例1：正常用户**
```
GET /get_circle_info.php
Authorization: Bearer [token]

预期响应：
{
  "status": "success",
  "data": {
    "has_circle": true,
    "user_role": "member",
    "circle": {...}
  }
}
```

**测试用例2：被踢出用户（未读）**
```
GET /get_circle_info.php
Authorization: Bearer [token]

预期响应：
{
  "status": "success",
  "data": {
    "has_circle": false,
    "user_role": null,
    "circle": null,
    "removed_info": {
      "was_removed": true,
      "circle_name": "测试圈子",
      "removed_at": "2025-01-25 10:30:00",
      "removed_by_nickname": "创建者",
      "is_self_removed": false
    }
  }
}
```

**测试用例3：被踢出用户（已读）**
```
GET /get_circle_info.php
Authorization: Bearer [token]

预期响应：
{
  "status": "success",
  "data": {
    "has_circle": false,
    "user_role": null,
    "circle": null,
    "removed_info": {
      "was_removed": false
    }
  }
}
```

### 测试clear_removed_status.php
**测试用例**：
```
POST /clear_removed_status.php
Authorization: Bearer [token]

预期响应：
{
  "status": "success",
  "message": "已标记通知为已读"
}
```

## 数据库验证

### 验证被踢出记录
```sql
-- 查看用户的被踢出记录
SELECT cm.*, c.name as circle_name, u.nickname as removed_by_nickname
FROM circle_members cm
JOIN outfit_circles c ON cm.circle_id = c.id
LEFT JOIN users u ON cm.removed_by = u.id
WHERE cm.user_id = [用户ID] AND cm.status = 'removed'
ORDER BY cm.removed_at DESC;
```

### 验证已读状态
```sql
-- 查看已读状态（removed_at包含_read后缀）
SELECT * FROM circle_members 
WHERE user_id = [用户ID] 
AND status = 'removed' 
AND removed_at LIKE '%_read';
```

## 边界情况测试

### 1. 多次被踢出
- [ ] 用户被踢出多个圈子，只显示最新的通知
- [ ] 用户重新加入后再次被踢出，显示最新通知

### 2. 数据异常
- [ ] removed_by为空的情况
- [ ] 圈子被删除的情况
- [ ] 时间格式异常的处理

### 3. 并发情况
- [ ] 用户同时在多个设备上进入页面
- [ ] 通知标记已读的并发处理

## 用户体验验证

### 1. 通知内容
- [ ] 文字表述清晰易懂
- [ ] 信息完整（圈子名称、操作者、时间）
- [ ] 区分被踢出和主动退出

### 2. 视觉效果
- [ ] 弹框样式与系统一致
- [ ] 颜色区分合理（红色表示被踢出，蓝色表示主动退出）
- [ ] 时间显示友好

### 3. 交互体验
- [ ] 点击确认后弹框正常关闭
- [ ] 不会重复显示相同通知
- [ ] 操作流程顺畅

## 部署注意事项

1. **数据库兼容性**：确保现有的circle_members表数据不受影响
2. **API向下兼容**：新增字段不影响现有功能
3. **错误处理**：确保各种异常情况都有合理的处理
4. **性能影响**：新增查询对页面加载性能的影响

## 验收标准

- [ ] 被踢出用户能及时收到通知
- [ ] 通知内容准确完整
- [ ] 不会重复显示相同通知
- [ ] 主动退出和被踢出有明确区分
- [ ] 时间显示格式友好
- [ ] 各种边界情况处理正常
- [ ] 不影响现有功能的正常使用

## 后续优化建议

1. **推送通知**：考虑在用户被踢出时发送微信模板消息
2. **历史记录**：提供查看被踢出历史的功能
3. **申诉机制**：允许用户申请重新加入圈子
4. **批量操作**：支持批量踢出用户的通知处理

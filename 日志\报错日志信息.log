本地api日志：
[2025-07-25 22:26:03] 从请求头获取到token
[2025-07-25 22:26:03] 用户认证成功，用户ID: 3
[2025-07-25 22:26:03] 处理表单格式的面容分析请求
[2025-07-25 22:26:03] 处理面容分析请求
[2025-07-25 22:26:03] 接收到面容分析请求，风格偏好: 
[2025-07-25 22:26:03] 开始保存Base64图片，前缀: face_front_3_1753453563
[2025-07-25 22:26:03] 已提取Base64数据部分
[2025-07-25 22:26:03] Base64解码成功，数据大小: 365726 字节
[2025-07-25 22:26:03] 将保存图片到: uploads/face_analysis/face_front_3_1753453563_688393fb03d02.jpg
[2025-07-25 22:26:03] 图片保存成功，写入 365726 字节
[2025-07-25 22:26:03] 生成的URL: https://cyyg.alidog.cn/login_backend/uploads/face_analysis/face_front_3_1753453563_688393fb03d02.jpg
[2025-07-25 22:26:03] 开始保存Base64图片，前缀: face_side_3_1753453563
[2025-07-25 22:26:03] 已提取Base64数据部分
[2025-07-25 22:26:03] Base64解码成功，数据大小: 379145 字节
[2025-07-25 22:26:03] 将保存图片到: uploads/face_analysis/face_side_3_1753453563_688393fb05093.jpg
[2025-07-25 22:26:03] 图片保存成功，写入 379145 字节
[2025-07-25 22:26:03] 生成的URL: https://cyyg.alidog.cn/login_backend/uploads/face_analysis/face_side_3_1753453563_688393fb05093.jpg
[2025-07-25 22:26:03] 尝试上传正面照片到OSS
[2025-07-25 22:26:03] 解析后的本地文件路径: /www/wwwroot/cyyg.alidog.cn/login_backend/uploads/face_analysis/face_front_3_1753453563_688393fb03d02.jpg
[2025-07-25 22:26:03] 本地文件存在，大小: 365726 字节
[2025-07-25 22:26:03] OSS路径: photos/face_analysis/face_front_3_1753453563_688393fb03d02.jpg
[2025-07-25 22:26:03] 正面照片上传OSS成功: https://images.alidog.cn/photos/face_analysis/face_front_3_1753453563_688393fb03d02.jpg
[2025-07-25 22:26:03] 尝试上传侧面照片到OSS
[2025-07-25 22:26:03] 解析后的侧面照片本地文件路径: /www/wwwroot/cyyg.alidog.cn/login_backend/uploads/face_analysis/face_side_3_1753453563_688393fb05093.jpg
[2025-07-25 22:26:03] 侧面照片本地文件存在，大小: 379145 字节
[2025-07-25 22:26:03] 侧面照片OSS路径: photos/face_analysis/face_side_3_1753453563_688393fb05093.jpg
[2025-07-25 22:26:03] 侧面照片上传OSS成功: https://images.alidog.cn/photos/face_analysis/face_side_3_1753453563_688393fb05093.jpg
[2025-07-25 22:26:03] 分析任务已创建，ID: 92
[2025-07-25 22:26:03] 调用中转API，正面照片: https://cyyg.alidog.cn/login_backend/uploads/face_analysis/face_front_3_1753453563_688393fb03d02.jpg, 风格偏好: 
[2025-07-25 22:26:04] 中转API返回错误: 图片处理失败: 下载图片失败: https://cyyg.alidog.cn/login_backend/uploads/face_analysis/face_front_3_1753453563_688393fb03d02.jpg


中转api日志：
[2025-07-25 09:16:24] 接收到请求数据 请求解析成功
[2025-07-25 09:16:24] 处理URL方式请求
[2025-07-25 09:16:24] 下载图片: https://cyyg.alidog.cn/login_backend/uploads/face_analysis/face_front_3499_1753434983_68834b672a6ff.jpg
[2025-07-25 09:16:24] 下载图片失败: https://cyyg.alidog.cn/login_backend/uploads/face_analysis/face_front_3499_1753434983_68834b672a6ff.jpg
[2025-07-25 14:26:03] 接收到请求数据 请求解析成功
[2025-07-25 14:26:03] 处理URL方式请求
[2025-07-25 14:26:03] 下载图片: https://cyyg.alidog.cn/login_backend/uploads/face_analysis/face_front_3_1753453563_688393fb03d02.jpg
[2025-07-25 14:26:03] 下载图片失败: https://cyyg.alidog.cn/login_backend/uploads/face_analysis/face_front_3_1753453563_688393fb03d02.jpg

微信开发者调试信息：
开始分析，正面照片路径: http://tmp/XMqTxW1a4AFea19694bd9396e5ca84533a29b3f78744.png
upload.js? [sm]:125 正面照片路径类型: string
upload.js? [sm]:126 正面照片路径长度: 59
upload.js? [sm]:177 使用token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIzIiwib3BlbmlkIjoib0FyOEY3RDExXzV1RVRDYXZ3S0xlNW05VC1ZVSIsImlhdCI6MTc1MzQ0MTU3OCwiZXhwIjoxNzU0MDQ2Mzc4fQ==.69d85af0d903d0dc87aabedc9a221b55afcd0e3d96c1917e198f6f64c0ffc945
upload.js? [sm]:178 是否为模拟用户: false
upload.js? [sm]:292 开始转换图片为Base64: http://tmp/XMqTxW1a4AFea19694bd9396e5ca84533a29b3f78744.png
upload.js? [sm]:313 图片转换为Base64成功，长度: 487658
upload.js? [sm]:292 开始转换图片为Base64: http://tmp/P9zcfaBoMyTBdaf6d2b6e31dfc90907c8b2e7a0eb6eb.png
upload.js? [sm]:313 图片转换为Base64成功，长度: 505550
upload.js? [sm]:200 开始上传Base64照片数据
upload.js? [sm]:218 上传响应: {error: true, msg: "面容分析失败，请稍后重试", data: null}data: nullerror: truemsg: "面容分析失败，请稍后重试"__proto__: Object
upload.js? [sm]:221 上传失败: 面容分析失败，请稍后重试(env: Windows,mp,1.06.2412050; lib: 3.8.9)
success @ upload.js? [sm]:221

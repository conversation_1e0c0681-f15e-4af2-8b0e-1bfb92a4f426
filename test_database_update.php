<?php
/**
 * 测试数据库更新脚本
 * 检查face_analysis表是否已添加支付相关字段
 */

require_once 'login_backend/db.php';

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 检查face_analysis表结构
    $stmt = $conn->prepare("DESCRIBE face_analysis");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "face_analysis表结构:\n";
    foreach ($columns as $column) {
        echo "- {$column['Field']}: {$column['Type']} {$column['Null']} {$column['Default']}\n";
    }
    
    // 检查是否有支付相关字段
    $hasPaymentStatus = false;
    $hasOrderId = false;
    $hasAmount = false;
    
    foreach ($columns as $column) {
        if ($column['Field'] === 'payment_status') {
            $hasPaymentStatus = true;
        }
        if ($column['Field'] === 'order_id') {
            $hasOrderId = true;
        }
        if ($column['Field'] === 'amount') {
            $hasAmount = true;
        }
    }
    
    echo "\n支付字段检查:\n";
    echo "- payment_status: " . ($hasPaymentStatus ? "✅ 存在" : "❌ 缺失") . "\n";
    echo "- order_id: " . ($hasOrderId ? "✅ 存在" : "❌ 缺失") . "\n";
    echo "- amount: " . ($hasAmount ? "✅ 存在" : "❌ 缺失") . "\n";
    
    if (!$hasPaymentStatus || !$hasOrderId || !$hasAmount) {
        echo "\n需要执行数据库迁移脚本！\n";
        echo "请执行: 最新数据库/add_face_analysis_payment_fields.sql\n";
    } else {
        echo "\n✅ 数据库结构正确！\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>

<?php
/**
 * 面容分析功能测试脚本
 * 用于测试整个面容分析流程
 */

header('Content-Type: application/json');

// 测试步骤
$testResults = [];

// 1. 测试网络连通性
$testResults['network_test'] = testNetworkConnectivity();

// 2. 测试图片URL访问
$testResults['image_url_test'] = testImageUrlAccess();

// 3. 测试Base64转换
$testResults['base64_test'] = testBase64Conversion();

// 4. 测试中转API调用
$testResults['api_test'] = testTransitApiCall();

echo json_encode([
    'timestamp' => date('Y-m-d H:i:s'),
    'test_results' => $testResults
], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

/**
 * 测试网络连通性
 */
function testNetworkConnectivity() {
    $testUrl = 'https://www.furrywoo.com/gemini/test_network.php';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $testUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'success' => $httpCode === 200 && empty($error),
        'http_code' => $httpCode,
        'error' => $error,
        'response' => $response ? json_decode($response, true) : null
    ];
}

/**
 * 测试图片URL访问
 */
function testImageUrlAccess() {
    $testImageUrl = 'https://cyyg.alidog.cn/login_backend/uploads/face_analysis/face_front_3_1753453563_688393fb03d02.jpg';
    
    // 测试从本地访问
    $localTest = testUrlFromLocal($testImageUrl);
    
    // 测试从中转服务器访问
    $remoteTest = testUrlFromRemote($testImageUrl);
    
    return [
        'test_url' => $testImageUrl,
        'local_access' => $localTest,
        'remote_access' => $remoteTest
    ];
}

/**
 * 从本地测试URL访问
 */
function testUrlFromLocal($url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'accessible' => $httpCode === 200 && empty($error),
        'http_code' => $httpCode,
        'content_type' => $contentType,
        'error' => $error
    ];
}

/**
 * 从中转服务器测试URL访问
 */
function testUrlFromRemote($url) {
    $testApiUrl = 'https://www.furrywoo.com/gemini/test_network.php';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $testApiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($httpCode === 200 && empty($error)) {
        $responseData = json_decode($response, true);
        if ($responseData && isset($responseData['test_results'])) {
            // 查找我们的测试URL结果
            foreach ($responseData['test_results'] as $result) {
                if (strpos($result['url'], 'face_front_3_1753453563') !== false) {
                    return $result;
                }
            }
        }
    }
    
    return [
        'accessible' => false,
        'error' => 'Unable to get remote test results',
        'api_error' => $error,
        'api_http_code' => $httpCode
    ];
}

/**
 * 测试Base64转换
 */
function testBase64Conversion() {
    // 创建一个简单的测试图片（1x1像素的PNG）
    $testImageData = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAHGbKdMDgAAAABJRU5ErkJggg==');
    
    // 转换为Base64
    $base64 = base64_encode($testImageData);
    $base64WithPrefix = 'data:image/png;base64,' . $base64;
    
    return [
        'success' => !empty($base64),
        'original_size' => strlen($testImageData),
        'base64_size' => strlen($base64),
        'base64_with_prefix_size' => strlen($base64WithPrefix),
        'sample_base64' => substr($base64, 0, 50) . '...'
    ];
}

/**
 * 测试中转API调用
 */
function testTransitApiCall() {
    // 使用测试图片的Base64数据
    $testImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAHGbKdMDgAAAABJRU5ErkJggg==';
    
    $data = [
        'front_photo_base64' => 'data:image/png;base64,' . $testImageBase64,
        'side_photo_base64' => '',
        'preferred_style' => '测试风格'
    ];
    
    $transitApiUrl = 'https://www.furrywoo.com/gemini/mianrongfenxi.php';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $transitApiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    $responseData = null;
    if ($response) {
        $responseData = json_decode($response, true);
    }
    
    return [
        'success' => $httpCode === 200 && empty($error),
        'http_code' => $httpCode,
        'error' => $error,
        'response_data' => $responseData,
        'response_preview' => $response ? substr($response, 0, 200) . '...' : null
    ];
}
?>

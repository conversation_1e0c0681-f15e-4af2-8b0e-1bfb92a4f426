<?php
/**
 * 测试Base64直传功能
 * 用于验证新的优化方案是否正常工作
 */

// 模拟一个小的测试图片的Base64数据（1x1像素的透明PNG）
$testImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==';

// 测试数据
$testData = [
    'front_photo_base64' => $testImageBase64,
    'preferred_style' => '测试风格偏好'
];

// 中转API地址
$transitApiUrl = 'https://www.furrywoo.com/gemini/mianrongfenxi.php';

echo "开始测试Base64直传功能...\n";
echo "测试图片Base64长度: " . strlen($testImageBase64) . "\n";
echo "中转API地址: $transitApiUrl\n\n";

// 发起POST请求
$ch = curl_init($transitApiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP状态码: $httpCode\n";
if ($error) {
    echo "CURL错误: $error\n";
}

echo "响应内容:\n";
if ($response) {
    $responseData = json_decode($response, true);
    if ($responseData) {
        echo json_encode($responseData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    } else {
        echo "响应不是有效的JSON:\n$response";
    }
} else {
    echo "无响应内容\n";
}

echo "\n\n测试完成。\n";
?>

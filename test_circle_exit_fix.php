<?php
/**
 * 测试圈子退出bug修复效果
 * 
 * 这个脚本用于验证用户退出圈子后是否能正常查看自己的衣物
 */

// 引入必要的文件
require_once 'login_backend/db.php';
require_once 'login_backend/auth.php';

// 设置测试参数
$testUserId = 2; // 替换为实际的测试用户ID
$testClothingId = 31860; // 替换为实际的测试衣物ID

echo "=== 圈子退出Bug修复测试 ===\n\n";

try {
    // 连接数据库
    $db = new Database();
    $conn = $db->getConnection();
    
    // 1. 检查测试衣物的基本信息
    echo "1. 检查测试衣物基本信息\n";
    $clothingSql = "SELECT id, user_id, name, circle_id FROM clothes WHERE id = :id";
    $clothingStmt = $conn->prepare($clothingSql);
    $clothingStmt->execute(['id' => $testClothingId]);
    $clothing = $clothingStmt->fetch(PDO::FETCH_ASSOC);
    
    if ($clothing) {
        echo "   衣物ID: {$clothing['id']}\n";
        echo "   所有者ID: {$clothing['user_id']}\n";
        echo "   衣物名称: {$clothing['name']}\n";
        echo "   圈子ID: " . ($clothing['circle_id'] ?? 'NULL') . "\n";
        
        $isOwner = $clothing['user_id'] == $testUserId;
        echo "   是否为测试用户的衣物: " . ($isOwner ? 'YES' : 'NO') . "\n\n";
        
        if (!$isOwner) {
            echo "警告: 测试衣物不属于测试用户，请更换测试参数\n\n";
        }
    } else {
        echo "   错误: 未找到测试衣物\n\n";
        exit(1);
    }
    
    // 2. 检查用户的圈子成员状态
    echo "2. 检查用户圈子成员状态\n";
    if ($clothing['circle_id']) {
        $memberSql = "SELECT status FROM circle_members WHERE user_id = :user_id AND circle_id = :circle_id";
        $memberStmt = $conn->prepare($memberSql);
        $memberStmt->execute(['user_id' => $testUserId, 'circle_id' => $clothing['circle_id']]);
        $member = $memberStmt->fetch(PDO::FETCH_ASSOC);
        
        if ($member) {
            echo "   圈子成员状态: {$member['status']}\n";
        } else {
            echo "   圈子成员状态: 不是成员\n";
        }
    } else {
        echo "   衣物不属于任何圈子\n";
    }
    echo "\n";
    
    // 3. 测试修复后的查询逻辑
    echo "3. 测试修复后的查询逻辑\n";
    
    // 3.1 测试优先所有权查询
    echo "   3.1 测试优先所有权查询\n";
    $ownerSql = "SELECT c.id, c.name, c.category, c.image_url, c.tags, c.description, c.created_at, c.wardrobe_id,
                        c.circle_id, u.nickname as creator_nickname,
                        CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
                 FROM clothes c
                 LEFT JOIN users u ON c.user_id = u.id
                 WHERE c.id = :clothing_id AND c.user_id = :user_id";
    
    $ownerStmt = $conn->prepare($ownerSql);
    $ownerStmt->execute(['clothing_id' => $testClothingId, 'user_id' => $testUserId]);
    $ownerResult = $ownerStmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "       查询结果数量: " . count($ownerResult) . "\n";
    if (count($ownerResult) > 0) {
        echo "       ✅ 所有权查询成功\n";
        echo "       数据源: {$ownerResult[0]['data_source']}\n";
    } else {
        echo "       ❌ 所有权查询失败\n";
    }
    echo "\n";
    
    // 3.2 测试圈子权限查询（如果所有权查询失败）
    if (count($ownerResult) == 0 && $clothing['circle_id']) {
        echo "   3.2 测试圈子权限查询\n";
        $circleSql = "SELECT c.id, c.name, c.category, c.image_url, c.tags, c.description, c.created_at, c.wardrobe_id,
                             c.circle_id, u.nickname as creator_nickname,
                             CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
                      FROM clothes c
                      LEFT JOIN users u ON c.user_id = u.id
                      WHERE c.id = :clothing_id AND c.circle_id IS NOT NULL 
                            AND c.circle_id IN (SELECT circle_id FROM circle_members WHERE user_id = :user_id AND status = 'active')";
        
        $circleStmt = $conn->prepare($circleSql);
        $circleStmt->execute(['clothing_id' => $testClothingId, 'user_id' => $testUserId]);
        $circleResult = $circleStmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "       查询结果数量: " . count($circleResult) . "\n";
        if (count($circleResult) > 0) {
            echo "       ✅ 圈子权限查询成功\n";
        } else {
            echo "       ❌ 圈子权限查询失败\n";
        }
        echo "\n";
    }
    
    // 4. 模拟API调用测试
    echo "4. 模拟API调用测试\n";
    
    $testCases = [
        ['data_source' => 'personal', 'include_circle_data' => 'true'],
        ['data_source' => 'all', 'include_circle_data' => 'true'],
        ['data_source' => 'personal', 'include_circle_data' => 'false'],
    ];
    
    foreach ($testCases as $index => $testCase) {
        echo "   测试案例 " . ($index + 1) . ": data_source={$testCase['data_source']}, include_circle_data={$testCase['include_circle_data']}\n";
        
        // 模拟API逻辑
        $includeCircleData = $testCase['include_circle_data'] === 'true';
        $dataSource = $testCase['data_source'];
        
        if ($includeCircleData) {
            // 使用修复后的逻辑
            $sql = "SELECT c.id, c.name, c.category, c.image_url, c.tags, c.description, c.created_at, c.wardrobe_id,
                           c.circle_id, u.nickname as creator_nickname,
                           CASE WHEN c.circle_id IS NULL THEN 'personal' ELSE 'shared' END as data_source
                    FROM clothes c
                    LEFT JOIN users u ON c.user_id = u.id
                    WHERE c.id = :clothing_id AND c.user_id = :user_id";
            
            $params = ['clothing_id' => $testClothingId, 'user_id' => $testUserId];
        } else {
            // 原有逻辑
            $sql = "SELECT id, name, category, image_url, tags, description, created_at, wardrobe_id 
                    FROM clothes 
                    WHERE id = :clothing_id AND user_id = :user_id";
            
            $params = ['clothing_id' => $testClothingId, 'user_id' => $testUserId];
        }
        
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "       结果数量: " . count($result) . "\n";
        if (count($result) > 0) {
            echo "       ✅ 查询成功\n";
        } else {
            echo "       ❌ 查询失败\n";
        }
        echo "\n";
    }
    
    echo "=== 测试完成 ===\n";
    echo "如果所有测试案例都显示'查询成功'，说明修复生效。\n";
    echo "如果仍有失败案例，请检查测试参数或代码逻辑。\n";
    
} catch (Exception $e) {
    echo "测试过程中发生错误: " . $e->getMessage() . "\n";
    exit(1);
}
?>

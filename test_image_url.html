<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片URL访问测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .test-url {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 3px;
            word-break: break-all;
            margin: 10px 0;
        }
        .test-image {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            margin: 10px 0;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .results {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>图片URL访问测试</h1>
        
        <div class="test-section">
            <h3>1. 直接图片访问测试</h3>
            <div class="test-url">https://cyyg.alidog.cn/login_backend/uploads/face_analysis/face_front_3_1753453563_688393fb03d02.jpg</div>
            <img id="testImage" class="test-image" 
                 src="https://cyyg.alidog.cn/login_backend/uploads/face_analysis/face_front_3_1753453563_688393fb03d02.jpg" 
                 alt="测试图片"
                 onload="imageLoadSuccess()" 
                 onerror="imageLoadError()">
            <div id="imageStatus" class="status">加载中...</div>
        </div>

        <div class="test-section">
            <h3>2. 网络连通性测试</h3>
            <button class="test-button" onclick="testNetworkConnectivity()">测试网络连通性</button>
            <div id="networkResults" class="results"></div>
        </div>

        <div class="test-section">
            <h3>3. 中转服务器访问测试</h3>
            <button class="test-button" onclick="testRemoteAccess()">测试中转服务器访问</button>
            <div id="remoteResults" class="results"></div>
        </div>

        <div class="test-section">
            <h3>4. 完整流程测试</h3>
            <button class="test-button" onclick="testFullProcess()">测试完整流程</button>
            <div id="fullResults" class="results"></div>
        </div>
    </div>

    <script>
        function imageLoadSuccess() {
            document.getElementById('imageStatus').innerHTML = '✓ 图片加载成功';
            document.getElementById('imageStatus').className = 'status success';
        }

        function imageLoadError() {
            document.getElementById('imageStatus').innerHTML = '✗ 图片加载失败';
            document.getElementById('imageStatus').className = 'status error';
        }

        async function testNetworkConnectivity() {
            const resultsDiv = document.getElementById('networkResults');
            resultsDiv.innerHTML = '测试中...';
            
            try {
                const response = await fetch('test_face_analysis.php');
                const data = await response.json();
                resultsDiv.innerHTML = JSON.stringify(data, null, 2);
            } catch (error) {
                resultsDiv.innerHTML = '测试失败: ' + error.message;
            }
        }

        async function testRemoteAccess() {
            const resultsDiv = document.getElementById('remoteResults');
            resultsDiv.innerHTML = '测试中...';
            
            try {
                const response = await fetch('https://www.furrywoo.com/gemini/test_network.php');
                const data = await response.json();
                resultsDiv.innerHTML = JSON.stringify(data, null, 2);
            } catch (error) {
                resultsDiv.innerHTML = '测试失败: ' + error.message;
            }
        }

        async function testFullProcess() {
            const resultsDiv = document.getElementById('fullResults');
            resultsDiv.innerHTML = '测试中...';
            
            // 创建一个简单的测试图片Base64
            const testImageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAHGbKdMDgAAAABJRU5ErkJggg==';
            
            const testData = {
                front_photo_base64: testImageBase64,
                side_photo_base64: '',
                preferred_style: '测试风格'
            };
            
            try {
                const response = await fetch('https://www.furrywoo.com/gemini/mianrongfenxi.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                
                const data = await response.json();
                resultsDiv.innerHTML = JSON.stringify(data, null, 2);
            } catch (error) {
                resultsDiv.innerHTML = '测试失败: ' + error.message;
            }
        }

        // 页面加载时自动测试图片
        window.onload = function() {
            // 图片测试会自动进行
        };
    </script>
</body>
</html>

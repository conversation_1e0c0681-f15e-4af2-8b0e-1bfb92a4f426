[2025-07-26 15:27:46] [API] API Request: GET /get_circle_clothes.php
Context: {
    "endpoint": "\/get_circle_clothes.php",
    "method": "GET",
    "params": {
        "user_id": "3",
        "page": 1,
        "per_page": 20,
        "category": "undefined",
        "wardrobe_id": null
    },
    "user_agent": "Mozilla\/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) Version\/15.0 Mobile\/15E148 Safari\/604.1 wechatdevtools\/1.06.2412050 MicroMessenger\/8.0.5 Language\/zh_CN webview\/",
    "ip": "*************"
}
--------------------------------------------------------------------------------
[2025-07-26 15:27:46] [DEBUG] 用户圈子查询结果
Context: {
    "user_id": "3",
    "user_circle": {
        "circle_id": "1",
        "role": "member",
        "circle_name": "19号营地"
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:27:46] [DEBUG] 圈子和用户基本信息
Context: {
    "user_id": "3",
    "circle_id": "1",
    "user_role": "member"
}
--------------------------------------------------------------------------------
[2025-07-26 15:27:46] [DEBUG] 圈子成员信息
Context: {
    "circle_id": "1",
    "members": [
        {
            "user_id": "1",
            "status": "active"
        },
        {
            "user_id": "3",
            "status": "active"
        }
    ]
}
--------------------------------------------------------------------------------
[2025-07-26 15:27:46] [DEBUG] 用户衣物统计
Context: {
    "user_id": "3",
    "total_clothes": "19"
}
--------------------------------------------------------------------------------
[2025-07-26 15:27:46] [DEBUG] 用户衣物circle_id分布
Context: {
    "user_id": "3",
    "circle_id": "1",
    "distribution": {
        "total": "19",
        "personal": "19",
        "shared": "0",
        "in_current_circle": "0"
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:27:46] [SQL] Database Query Executed
Context: {
    "sql": "主衣物查询",
    "params": "SELECT c.id, c.name, c.category, c.category_id, c.image_url, c.tags, \n                          c.description, c.created_at, c.updated_at, c.user_id, c.circle_id,\n                          c.wardrobe_id, c.wear_count, c.wear_frequency, c.wear_frequency_unit,\n                          u.nickname as creator_nickname,\n                          w.name as wardrobe_name\n                   FROM clothes c\n                   JOIN users u ON c.user_id = u.id\n                   LEFT JOIN wardrobes w ON c.wardrobe_id = w.id\n                   WHERE (c.circle_id = :circle_id OR \n                          (c.user_id IN (SELECT user_id FROM circle_members \n                                        WHERE circle_id = :circle_id AND status = 'active') \n                           AND c.circle_id IS NULL)) AND c.category = :category\n                   ORDER BY c.created_at DESC\n                   LIMIT :offset, :per_page",
    "result_count": 4,
    "result_sample": {
        "params": {
            "circle_id": "1",
            "category": "undefined"
        },
        "offset": 0,
        "per_page": 20
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:27:46] [DEBUG] 用户衣物JOIN测试
Context: {
    "user_id": "3",
    "count_with_join": "19"
}
--------------------------------------------------------------------------------
[2025-07-26 15:27:46] [DEBUG] 主查询执行结果
Context: {
    "row_count": 0,
    "expected_more_than_zero": false
}
--------------------------------------------------------------------------------
[2025-07-26 15:27:46] [SQL] Database Query Executed
Context: {
    "sql": "分类统计查询",
    "params": "SELECT c.category, COUNT(*) as count\n                    FROM clothes c\n                    JOIN users u ON c.user_id = u.id\n                    WHERE (c.circle_id = :circle_id OR\n                           (c.user_id IN (SELECT user_id FROM circle_members\n                                         WHERE circle_id = :circle_id AND status = 'active')\n                            AND c.circle_id IS NULL))\n                    GROUP BY c.category\n                    ORDER BY count DESC",
    "result_count": 2,
    "result_sample": {
        "circle_id": "1",
        "row_count": 7
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:27:46] [DEBUG] 分类统计结果
Context: {
    "category_stats": [
        {
            "category": "skirts",
            "count": 9
        },
        {
            "category": "tops",
            "count": 8
        },
        {
            "category": "pants",
            "count": 7
        },
        {
            "category": "coats",
            "count": 3
        },
        {
            "category": "accessories",
            "count": 3
        },
        {
            "category": "shoes",
            "count": 2
        },
        {
            "category": "bags",
            "count": 2
        }
    ],
    "total_categories": 7
}
--------------------------------------------------------------------------------
[2025-07-26 15:27:46] [API] API Request: GET /get_circle_clothes.php
Context: {
    "endpoint": "\/get_circle_clothes.php",
    "method": "GET",
    "params": {
        "user_id": "3",
        "circle_id": "1"
    },
    "user_agent": "Mozilla\/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) Version\/15.0 Mobile\/15E148 Safari\/604.1 wechatdevtools\/1.06.2412050 MicroMessenger\/8.0.5 Language\/zh_CN webview\/",
    "ip": "*************",
    "response": {
        "clothes_count": 0,
        "category_stats_count": 7,
        "total_count": "0"
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:05] [API] API Request: GET /get_circle_clothes.php
Context: {
    "endpoint": "\/get_circle_clothes.php",
    "method": "GET",
    "params": {
        "user_id": "3",
        "page": 1,
        "per_page": 20,
        "category": null,
        "wardrobe_id": null
    },
    "user_agent": "Mozilla\/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) Version\/15.0 Mobile\/15E148 Safari\/604.1 wechatdevtools\/1.06.2412050 MicroMessenger\/8.0.5 Language\/zh_CN webview\/",
    "ip": "*************"
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:05] [DEBUG] 用户圈子查询结果
Context: {
    "user_id": "3",
    "user_circle": {
        "circle_id": "1",
        "role": "member",
        "circle_name": "19号营地"
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:05] [DEBUG] 圈子和用户基本信息
Context: {
    "user_id": "3",
    "circle_id": "1",
    "user_role": "member"
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:05] [DEBUG] 圈子成员信息
Context: {
    "circle_id": "1",
    "members": [
        {
            "user_id": "1",
            "status": "active"
        },
        {
            "user_id": "3",
            "status": "active"
        }
    ]
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:05] [DEBUG] 用户衣物统计
Context: {
    "user_id": "3",
    "total_clothes": "19"
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:05] [DEBUG] 用户衣物circle_id分布
Context: {
    "user_id": "3",
    "circle_id": "1",
    "distribution": {
        "total": "19",
        "personal": "0",
        "shared": "19",
        "in_current_circle": "19"
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:05] [SQL] Database Query Executed
Context: {
    "sql": "主衣物查询",
    "params": "SELECT c.id, c.name, c.category, c.category_id, c.image_url, c.tags, \n                          c.description, c.created_at, c.updated_at, c.user_id, c.circle_id,\n                          c.wardrobe_id, c.wear_count, c.wear_frequency, c.wear_frequency_unit,\n                          u.nickname as creator_nickname,\n                          w.name as wardrobe_name\n                   FROM clothes c\n                   JOIN users u ON c.user_id = u.id\n                   LEFT JOIN wardrobes w ON c.wardrobe_id = w.id\n                   WHERE (c.circle_id = :circle_id OR \n                          (c.user_id IN (SELECT user_id FROM circle_members \n                                        WHERE circle_id = :circle_id AND status = 'active') \n                           AND c.circle_id IS NULL))\n                   ORDER BY c.created_at DESC\n                   LIMIT :offset, :per_page",
    "result_count": 4,
    "result_sample": {
        "params": {
            "circle_id": "1"
        },
        "offset": 0,
        "per_page": 20
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:05] [DEBUG] 用户衣物JOIN测试
Context: {
    "user_id": "3",
    "count_with_join": "19"
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:05] [DEBUG] 主查询执行结果
Context: {
    "row_count": 20,
    "expected_more_than_zero": true
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:05] [SQL] Database Query Executed
Context: {
    "sql": "分类统计查询",
    "params": "SELECT c.category, COUNT(*) as count\n                    FROM clothes c\n                    JOIN users u ON c.user_id = u.id\n                    WHERE (c.circle_id = :circle_id OR\n                           (c.user_id IN (SELECT user_id FROM circle_members\n                                         WHERE circle_id = :circle_id AND status = 'active')\n                            AND c.circle_id IS NULL))\n                    GROUP BY c.category\n                    ORDER BY count DESC",
    "result_count": 2,
    "result_sample": {
        "circle_id": "1",
        "row_count": 7
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:05] [DEBUG] 分类统计结果
Context: {
    "category_stats": [
        {
            "category": "skirts",
            "count": 9
        },
        {
            "category": "tops",
            "count": 8
        },
        {
            "category": "pants",
            "count": 7
        },
        {
            "category": "accessories",
            "count": 3
        },
        {
            "category": "coats",
            "count": 3
        },
        {
            "category": "bags",
            "count": 2
        },
        {
            "category": "shoes",
            "count": 2
        }
    ],
    "total_categories": 7
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:05] [API] API Request: GET /get_circle_clothes.php
Context: {
    "endpoint": "\/get_circle_clothes.php",
    "method": "GET",
    "params": {
        "user_id": "3",
        "circle_id": "1"
    },
    "user_agent": "Mozilla\/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) Version\/15.0 Mobile\/15E148 Safari\/604.1 wechatdevtools\/1.06.2412050 MicroMessenger\/8.0.5 Language\/zh_CN webview\/",
    "ip": "*************",
    "response": {
        "clothes_count": 20,
        "category_stats_count": 7,
        "total_count": "34"
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:11] [API] API Request: GET /get_circle_clothes.php
Context: {
    "endpoint": "\/get_circle_clothes.php",
    "method": "GET",
    "params": {
        "user_id": "3",
        "page": 1,
        "per_page": 20,
        "category": null,
        "wardrobe_id": null
    },
    "user_agent": "Mozilla\/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) Version\/15.0 Mobile\/15E148 Safari\/604.1 wechatdevtools\/1.06.2412050 MicroMessenger\/8.0.5 Language\/zh_CN webview\/",
    "ip": "*************"
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:11] [DEBUG] 用户圈子查询结果
Context: {
    "user_id": "3",
    "user_circle": {
        "circle_id": "1",
        "role": "member",
        "circle_name": "19号营地"
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:11] [DEBUG] 圈子和用户基本信息
Context: {
    "user_id": "3",
    "circle_id": "1",
    "user_role": "member"
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:11] [DEBUG] 圈子成员信息
Context: {
    "circle_id": "1",
    "members": [
        {
            "user_id": "1",
            "status": "active"
        },
        {
            "user_id": "3",
            "status": "active"
        }
    ]
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:11] [DEBUG] 用户衣物统计
Context: {
    "user_id": "3",
    "total_clothes": "19"
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:11] [DEBUG] 用户衣物circle_id分布
Context: {
    "user_id": "3",
    "circle_id": "1",
    "distribution": {
        "total": "19",
        "personal": "0",
        "shared": "19",
        "in_current_circle": "19"
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:11] [SQL] Database Query Executed
Context: {
    "sql": "主衣物查询",
    "params": "SELECT c.id, c.name, c.category, c.category_id, c.image_url, c.tags, \n                          c.description, c.created_at, c.updated_at, c.user_id, c.circle_id,\n                          c.wardrobe_id, c.wear_count, c.wear_frequency, c.wear_frequency_unit,\n                          u.nickname as creator_nickname,\n                          w.name as wardrobe_name\n                   FROM clothes c\n                   JOIN users u ON c.user_id = u.id\n                   LEFT JOIN wardrobes w ON c.wardrobe_id = w.id\n                   WHERE (c.circle_id = :circle_id OR \n                          (c.user_id IN (SELECT user_id FROM circle_members \n                                        WHERE circle_id = :circle_id AND status = 'active') \n                           AND c.circle_id IS NULL))\n                   ORDER BY c.created_at DESC\n                   LIMIT :offset, :per_page",
    "result_count": 4,
    "result_sample": {
        "params": {
            "circle_id": "1"
        },
        "offset": 0,
        "per_page": 20
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:11] [DEBUG] 用户衣物JOIN测试
Context: {
    "user_id": "3",
    "count_with_join": "19"
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:11] [DEBUG] 主查询执行结果
Context: {
    "row_count": 20,
    "expected_more_than_zero": true
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:11] [SQL] Database Query Executed
Context: {
    "sql": "分类统计查询",
    "params": "SELECT c.category, COUNT(*) as count\n                    FROM clothes c\n                    JOIN users u ON c.user_id = u.id\n                    WHERE (c.circle_id = :circle_id OR\n                           (c.user_id IN (SELECT user_id FROM circle_members\n                                         WHERE circle_id = :circle_id AND status = 'active')\n                            AND c.circle_id IS NULL))\n                    GROUP BY c.category\n                    ORDER BY count DESC",
    "result_count": 2,
    "result_sample": {
        "circle_id": "1",
        "row_count": 7
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:11] [DEBUG] 分类统计结果
Context: {
    "category_stats": [
        {
            "category": "skirts",
            "count": 9
        },
        {
            "category": "tops",
            "count": 8
        },
        {
            "category": "pants",
            "count": 7
        },
        {
            "category": "accessories",
            "count": 3
        },
        {
            "category": "coats",
            "count": 3
        },
        {
            "category": "bags",
            "count": 2
        },
        {
            "category": "shoes",
            "count": 2
        }
    ],
    "total_categories": 7
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:11] [API] API Request: GET /get_circle_clothes.php
Context: {
    "endpoint": "\/get_circle_clothes.php",
    "method": "GET",
    "params": {
        "user_id": "3",
        "circle_id": "1"
    },
    "user_agent": "Mozilla\/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) Version\/15.0 Mobile\/15E148 Safari\/604.1 wechatdevtools\/1.06.2412050 MicroMessenger\/8.0.5 Language\/zh_CN webview\/",
    "ip": "*************",
    "response": {
        "clothes_count": 20,
        "category_stats_count": 7,
        "total_count": "34"
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:31] [API] API Request: GET /get_circle_clothes.php
Context: {
    "endpoint": "\/get_circle_clothes.php",
    "method": "GET",
    "params": {
        "user_id": "3",
        "page": 1,
        "per_page": 20,
        "category": null,
        "wardrobe_id": null
    },
    "user_agent": "Mozilla\/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) Version\/15.0 Mobile\/15E148 Safari\/604.1 wechatdevtools\/1.06.2412050 MicroMessenger\/8.0.5 Language\/zh_CN webview\/",
    "ip": "*************"
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:31] [DEBUG] 用户圈子查询结果
Context: {
    "user_id": "3",
    "user_circle": {
        "circle_id": "1",
        "role": "member",
        "circle_name": "19号营地"
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:31] [DEBUG] 圈子和用户基本信息
Context: {
    "user_id": "3",
    "circle_id": "1",
    "user_role": "member"
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:31] [DEBUG] 圈子成员信息
Context: {
    "circle_id": "1",
    "members": [
        {
            "user_id": "1",
            "status": "active"
        },
        {
            "user_id": "3",
            "status": "active"
        }
    ]
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:31] [DEBUG] 用户衣物统计
Context: {
    "user_id": "3",
    "total_clothes": "19"
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:31] [DEBUG] 用户衣物circle_id分布
Context: {
    "user_id": "3",
    "circle_id": "1",
    "distribution": {
        "total": "19",
        "personal": "0",
        "shared": "19",
        "in_current_circle": "19"
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:31] [SQL] Database Query Executed
Context: {
    "sql": "主衣物查询",
    "params": "SELECT c.id, c.name, c.category, c.category_id, c.image_url, c.tags, \n                          c.description, c.created_at, c.updated_at, c.user_id, c.circle_id,\n                          c.wardrobe_id, c.wear_count, c.wear_frequency, c.wear_frequency_unit,\n                          u.nickname as creator_nickname,\n                          w.name as wardrobe_name\n                   FROM clothes c\n                   JOIN users u ON c.user_id = u.id\n                   LEFT JOIN wardrobes w ON c.wardrobe_id = w.id\n                   WHERE (c.circle_id = :circle_id OR \n                          (c.user_id IN (SELECT user_id FROM circle_members \n                                        WHERE circle_id = :circle_id AND status = 'active') \n                           AND c.circle_id IS NULL))\n                   ORDER BY c.created_at DESC\n                   LIMIT :offset, :per_page",
    "result_count": 4,
    "result_sample": {
        "params": {
            "circle_id": "1"
        },
        "offset": 0,
        "per_page": 20
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:31] [DEBUG] 用户衣物JOIN测试
Context: {
    "user_id": "3",
    "count_with_join": "19"
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:31] [DEBUG] 主查询执行结果
Context: {
    "row_count": 20,
    "expected_more_than_zero": true
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:31] [SQL] Database Query Executed
Context: {
    "sql": "分类统计查询",
    "params": "SELECT c.category, COUNT(*) as count\n                    FROM clothes c\n                    JOIN users u ON c.user_id = u.id\n                    WHERE (c.circle_id = :circle_id OR\n                           (c.user_id IN (SELECT user_id FROM circle_members\n                                         WHERE circle_id = :circle_id AND status = 'active')\n                            AND c.circle_id IS NULL))\n                    GROUP BY c.category\n                    ORDER BY count DESC",
    "result_count": 2,
    "result_sample": {
        "circle_id": "1",
        "row_count": 7
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:31] [DEBUG] 分类统计结果
Context: {
    "category_stats": [
        {
            "category": "skirts",
            "count": 9
        },
        {
            "category": "tops",
            "count": 8
        },
        {
            "category": "pants",
            "count": 7
        },
        {
            "category": "accessories",
            "count": 3
        },
        {
            "category": "coats",
            "count": 3
        },
        {
            "category": "bags",
            "count": 2
        },
        {
            "category": "shoes",
            "count": 2
        }
    ],
    "total_categories": 7
}
--------------------------------------------------------------------------------
[2025-07-26 15:30:31] [API] API Request: GET /get_circle_clothes.php
Context: {
    "endpoint": "\/get_circle_clothes.php",
    "method": "GET",
    "params": {
        "user_id": "3",
        "circle_id": "1"
    },
    "user_agent": "Mozilla\/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) Version\/15.0 Mobile\/15E148 Safari\/604.1 wechatdevtools\/1.06.2412050 MicroMessenger\/8.0.5 Language\/zh_CN webview\/",
    "ip": "*************",
    "response": {
        "clothes_count": 20,
        "category_stats_count": 7,
        "total_count": "34"
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:31:34] [API] API Request: GET /get_circle_clothes.php
Context: {
    "endpoint": "\/get_circle_clothes.php",
    "method": "GET",
    "params": {
        "user_id": "3",
        "page": 1,
        "per_page": 20,
        "category": null,
        "wardrobe_id": null
    },
    "user_agent": "Mozilla\/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) Version\/15.0 Mobile\/15E148 Safari\/604.1 wechatdevtools\/1.06.2412050 MicroMessenger\/8.0.5 Language\/zh_CN webview\/",
    "ip": "*************"
}
--------------------------------------------------------------------------------
[2025-07-26 15:31:34] [DEBUG] 用户圈子查询结果
Context: {
    "user_id": "3",
    "user_circle": {
        "circle_id": "1",
        "role": "member",
        "circle_name": "19号营地"
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:31:34] [DEBUG] 圈子和用户基本信息
Context: {
    "user_id": "3",
    "circle_id": "1",
    "user_role": "member"
}
--------------------------------------------------------------------------------
[2025-07-26 15:31:34] [DEBUG] 圈子成员信息
Context: {
    "circle_id": "1",
    "members": [
        {
            "user_id": "1",
            "status": "active"
        },
        {
            "user_id": "3",
            "status": "active"
        }
    ]
}
--------------------------------------------------------------------------------
[2025-07-26 15:31:34] [DEBUG] 用户衣物统计
Context: {
    "user_id": "3",
    "total_clothes": "19"
}
--------------------------------------------------------------------------------
[2025-07-26 15:31:34] [DEBUG] 用户衣物circle_id分布
Context: {
    "user_id": "3",
    "circle_id": "1",
    "distribution": {
        "total": "19",
        "personal": "0",
        "shared": "19",
        "in_current_circle": "19"
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:31:34] [SQL] Database Query Executed
Context: {
    "sql": "主衣物查询",
    "params": "SELECT c.id, c.name, c.category, c.category_id, c.image_url, c.tags, \n                          c.description, c.created_at, c.updated_at, c.user_id, c.circle_id,\n                          c.wardrobe_id, c.wear_count, c.wear_frequency, c.wear_frequency_unit,\n                          u.nickname as creator_nickname,\n                          w.name as wardrobe_name\n                   FROM clothes c\n                   JOIN users u ON c.user_id = u.id\n                   LEFT JOIN wardrobes w ON c.wardrobe_id = w.id\n                   WHERE (c.circle_id = :circle_id OR \n                          (c.user_id IN (SELECT user_id FROM circle_members \n                                        WHERE circle_id = :circle_id AND status = 'active') \n                           AND c.circle_id IS NULL))\n                   ORDER BY c.created_at DESC\n                   LIMIT :offset, :per_page",
    "result_count": 4,
    "result_sample": {
        "params": {
            "circle_id": "1"
        },
        "offset": 0,
        "per_page": 20
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:31:34] [DEBUG] 用户衣物JOIN测试
Context: {
    "user_id": "3",
    "count_with_join": "19"
}
--------------------------------------------------------------------------------
[2025-07-26 15:31:34] [DEBUG] 主查询执行结果
Context: {
    "row_count": 20,
    "expected_more_than_zero": true
}
--------------------------------------------------------------------------------
[2025-07-26 15:31:34] [SQL] Database Query Executed
Context: {
    "sql": "分类统计查询",
    "params": "SELECT c.category, COUNT(*) as count\n                    FROM clothes c\n                    JOIN users u ON c.user_id = u.id\n                    WHERE (c.circle_id = :circle_id OR\n                           (c.user_id IN (SELECT user_id FROM circle_members\n                                         WHERE circle_id = :circle_id AND status = 'active')\n                            AND c.circle_id IS NULL))\n                    GROUP BY c.category\n                    ORDER BY count DESC",
    "result_count": 2,
    "result_sample": {
        "circle_id": "1",
        "row_count": 7
    }
}
--------------------------------------------------------------------------------
[2025-07-26 15:31:34] [DEBUG] 分类统计结果
Context: {
    "category_stats": [
        {
            "category": "skirts",
            "count": 9
        },
        {
            "category": "tops",
            "count": 8
        },
        {
            "category": "pants",
            "count": 7
        },
        {
            "category": "accessories",
            "count": 3
        },
        {
            "category": "coats",
            "count": 3
        },
        {
            "category": "shoes",
            "count": 2
        },
        {
            "category": "bags",
            "count": 2
        }
    ],
    "total_categories": 7
}
--------------------------------------------------------------------------------
[2025-07-26 15:31:34] [API] API Request: GET /get_circle_clothes.php
Context: {
    "endpoint": "\/get_circle_clothes.php",
    "method": "GET",
    "params": {
        "user_id": "3",
        "circle_id": "1"
    },
    "user_agent": "Mozilla\/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) Version\/15.0 Mobile\/15E148 Safari\/604.1 wechatdevtools\/1.06.2412050 MicroMessenger\/8.0.5 Language\/zh_CN webview\/",
    "ip": "*************",
    "response": {
        "clothes_count": 20,
        "category_stats_count": 7,
        "total_count": "34"
    }
}
--------------------------------------------------------------------------------

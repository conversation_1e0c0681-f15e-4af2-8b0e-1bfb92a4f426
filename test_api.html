<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        input[type="text"] {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
            width: 200px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>穿搭圈子API测试页面</h1>
        
        <div class="test-section">
            <h3>1. 设置API基础URL和Token</h3>
            <input type="text" id="apiBaseUrl" placeholder="API基础URL" value="http://localhost/login_backend">
            <input type="text" id="authToken" placeholder="授权Token">
            <button onclick="saveConfig()">保存配置</button>
            <div id="configResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. 测试调试版圈子信息API</h3>
            <button onclick="testDebugCircleInfo()">测试 debug_circle_info.php</button>
            <div id="debugResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. 测试正式版圈子信息API</h3>
            <button onclick="testCircleInfo()">测试 get_circle_info.php</button>
            <div id="circleResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>4. 测试成员列表API</h3>
            <button onclick="testCircleMembers()">测试 get_circle_members.php</button>
            <div id="membersResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>5. 测试成员贡献API</h3>
            <input type="text" id="targetUserId" placeholder="目标用户ID（可选）">
            <button onclick="testMemberContributions()">测试 get_member_contributions.php</button>
            <div id="contributionsResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        let apiBaseUrl = 'http://localhost/login_backend';
        let authToken = '';

        function saveConfig() {
            apiBaseUrl = document.getElementById('apiBaseUrl').value;
            authToken = document.getElementById('authToken').value;
            
            const result = document.getElementById('configResult');
            result.style.display = 'block';
            result.className = 'result success';
            result.textContent = `配置已保存:\nAPI URL: ${apiBaseUrl}\nToken: ${authToken ? '已设置' : '未设置'}`;
        }

        function makeRequest(endpoint, params = {}) {
            const url = new URL(`${apiBaseUrl}/${endpoint}`);
            Object.keys(params).forEach(key => {
                if (params[key]) url.searchParams.append(key, params[key]);
            });

            const headers = {
                'Content-Type': 'application/json'
            };

            if (authToken) {
                headers['Authorization'] = `Bearer ${authToken}`;
            }

            return fetch(url, {
                method: 'GET',
                headers: headers
            });
        }

        function displayResult(elementId, response, data) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            
            if (response.ok) {
                element.className = 'result success';
                element.textContent = JSON.stringify(data, null, 2);
            } else {
                element.className = 'result error';
                element.textContent = `错误 ${response.status}: ${response.statusText}\n${JSON.stringify(data, null, 2)}`;
            }
        }

        async function testDebugCircleInfo() {
            try {
                const response = await makeRequest('debug_circle_info.php');
                const data = await response.json();
                displayResult('debugResult', response, data);
            } catch (error) {
                const element = document.getElementById('debugResult');
                element.style.display = 'block';
                element.className = 'result error';
                element.textContent = `网络错误: ${error.message}`;
            }
        }

        async function testCircleInfo() {
            try {
                const response = await makeRequest('get_circle_info.php');
                const data = await response.json();
                displayResult('circleResult', response, data);
            } catch (error) {
                const element = document.getElementById('circleResult');
                element.style.display = 'block';
                element.className = 'result error';
                element.textContent = `网络错误: ${error.message}`;
            }
        }

        async function testCircleMembers() {
            try {
                const response = await makeRequest('get_circle_members.php');
                const data = await response.json();
                displayResult('membersResult', response, data);
            } catch (error) {
                const element = document.getElementById('membersResult');
                element.style.display = 'block';
                element.className = 'result error';
                element.textContent = `网络错误: ${error.message}`;
            }
        }

        async function testMemberContributions() {
            try {
                const targetUserId = document.getElementById('targetUserId').value;
                const params = {};
                if (targetUserId) {
                    params.user_id = targetUserId;
                }
                
                const response = await makeRequest('get_member_contributions.php', params);
                const data = await response.json();
                displayResult('contributionsResult', response, data);
            } catch (error) {
                const element = document.getElementById('contributionsResult');
                element.style.display = 'block';
                element.className = 'result error';
                element.textContent = `网络错误: ${error.message}`;
            }
        }
    </script>
</body>
</html>
